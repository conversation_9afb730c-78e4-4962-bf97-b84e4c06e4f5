import logging
import time
import random
import concurrent.futures
from enum import Enum
from typing import List, Dict, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor
import pandas as pd
import os
from datetime import datetime
import requests
from urllib.parse import quote
import re

from selenium.common.exceptions import NoSuchElementException, TimeoutException, WebDriverException
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.remote.webelement import WebElement
from selenium import webdriver  # Use regular selenium by default
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC

from amazon_scraper.models import Product
from amazon_scraper.db_helper import DatabaseHelper

# Handle both relative and absolute imports for proxy config
try:
    from .proxy_config import PROXY_LIST
except ImportError:
    from proxy_config import PROXY_LIST
# Configure logging for better error visibility
logging.getLogger().setLevel(logging.INFO)  # Changed from ERROR to INFO for better visibility

# Enhanced formatter with timestamp and logger name
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
handler = logging.StreamHandler()
handler.setFormatter(formatter)

# Configure main scraper logger
scraper_logger = logging.getLogger('amazon_scraper.scraper')
scraper_logger.setLevel(logging.INFO)
scraper_logger.addHandler(handler)

price_logger = logging.getLogger('amazon_scraper.price_parser')
price_logger.setLevel(logging.INFO)
price_logger.addHandler(handler)

logging.getLogger("WDM").setLevel(logging.ERROR)
logging.getLogger("seleniumwire").setLevel(logging.ERROR)
logging.getLogger("urllib3").setLevel(logging.ERROR)

class DriverInitializationError(BaseException):
    message = "Unable to initialize Chrome webdriver for scraping."

class DriverGetProductsError(BaseException):
    message = "Unable to get Amazon product data with Chrome webdriver."

class MissingProductDataError(BaseException):
    message = "Missing required data for product."

class ProxyError(Exception):
    """Base class for proxy-related errors"""
    pass

class ProxyValidationError(ProxyError):
    """Raised when proxy validation fails"""
    def __init__(self, proxy_address: str, reason: str):
        self.proxy_address = proxy_address
        self.reason = reason
        super().__init__(f"Proxy {proxy_address} validation failed: {reason}")

class NoWorkingProxyError(ProxyError):
    """Raised when no working proxies are found"""
    def __init__(self, attempted_proxies: int):
        self.attempted_proxies = attempted_proxies
        super().__init__(f"No working proxies found after testing {attempted_proxies} proxies")

class AmazonBlockingError(Exception):
    """Raised when Amazon appears to be blocking requests"""
    def __init__(self, reason: str, url: str = None):
        self.reason = reason
        self.url = url
        super().__init__(f"Amazon blocking detected: {reason}" + (f" (URL: {url})" if url else ""))

class PageScrapingError(Exception):
    """Raised when page scraping fails with detailed context"""
    def __init__(self, reason: str, url: str = None, page_title: str = None, status_code: int = None):
        self.reason = reason
        self.url = url
        self.page_title = page_title
        self.status_code = status_code
        super().__init__(f"Page scraping failed: {reason}" +
                        (f" (URL: {url})" if url else "") +
                        (f" (Title: {page_title})" if page_title else "") +
                        (f" (Status: {status_code})" if status_code else ""))

class ProxyHealthTracker:
    """Tracks proxy health and failure patterns"""
    def __init__(self):
        self.proxy_failures: Dict[str, int] = {}
        self.proxy_last_success: Dict[str, float] = {}
        self.proxy_blacklist: set = set()

    def record_failure(self, proxy_address: str, reason: str = ""):
        """Record a proxy failure"""
        self.proxy_failures[proxy_address] = self.proxy_failures.get(proxy_address, 0) + 1
        if self.proxy_failures[proxy_address] >= 3:  # Blacklist after 3 failures
            self.proxy_blacklist.add(proxy_address)

    def record_success(self, proxy_address: str):
        """Record a proxy success"""
        self.proxy_last_success[proxy_address] = time.time()
        # Reset failure count on success
        if proxy_address in self.proxy_failures:
            self.proxy_failures[proxy_address] = 0
        # Remove from blacklist if it was there
        self.proxy_blacklist.discard(proxy_address)

    def is_blacklisted(self, proxy_address: str) -> bool:
        """Check if proxy is blacklisted"""
        return proxy_address in self.proxy_blacklist

    def get_failure_count(self, proxy_address: str) -> int:
        """Get failure count for a proxy"""
        return self.proxy_failures.get(proxy_address, 0)

class ProductXPath(str, Enum):
    PRODUCTS = "//div[@data-component-type='s-search-result']"
    TITLE = ".//a/h2/span"
    URL = ".//a/h2"
    PRICE_FULL = ".//span[contains(@class, 'a-price')]//span[contains(@class, 'a-offscreen')]"
    NEXT_PAGE = "//a[contains(@class,'s-pagination-next')]"

class AmazonScraper:
    """Class for scraping Amazon"""

    def __init__(self, logger: logging.Logger | None = None, use_proxy: bool = True, advanced_proxy: bool = False, http_only_proxies: bool = True) -> None:
            self._logger = logger if logger else logging.getLogger(__name__)
            self.db = DatabaseHelper()  # Initialize database helper
            self.use_proxy = use_proxy  # Always use proxy by default
            self.advanced_proxy = advanced_proxy  # Use selenium-wire for advanced proxy features
            self.http_only_proxies = http_only_proxies  # Optimize for HTTP-only proxies
            self.proxy_health = ProxyHealthTracker()  # Track proxy health
            self.retry_count = 0  # Track retry attempts
            self.max_retries = 3  # Maximum retry attempts

    def _get_random_user_agent(self) -> str:
        user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:123.0) Gecko/20100101 Firefox/123.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3 Safari/605.1.15",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Edge/*********",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (iPhone; CPU iPhone OS 17_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3 Mobile/15E148 Safari/604.1",
            "Mozilla/5.0 (iPad; CPU OS 17_3_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.3 Mobile/15E148 Safari/604.1",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********"
        ]
        return random.choice(user_agents)

    def _add_headers_to_request(self, request) -> None:
        """Intercepts selenium requests to add randomized headers"""
        headers = {
            "User-Agent": self._get_random_user_agent(),
            "Accept-Language": random.choice(["en-US,en;q=0.9", "it-IT,it;q=0.9"]),
            "Accept-Encoding": "gzip, deflate, br",
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
            "Connection": "keep-alive",
            "Referer": "https://www.amazon.it/",
            "Host": "www.amazon.it",
            "TE": "Trailers",
        }
        for key, value in headers.items():
            request.headers[key] = value

    def _init_chrome_driver(self):
        """Initializes Chrome webdriver - always with proxy, different implementations for speed vs features"""
        chrome_options = Options()

        # Optimized headless settings for speed
        chrome_options.add_argument("--headless=new")
        chrome_options.add_argument("--window-size=1920,1080")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--disable-dev-shm-usage")

        # Ultra-aggressive performance options for maximum speed
        chrome_options.add_argument("--disable-extensions")
        chrome_options.add_argument("--disable-infobars")
        chrome_options.add_argument("--disable-notifications")
        chrome_options.add_argument("--disable-popup-blocking")
        chrome_options.add_argument("--disable-background-timer-throttling")
        chrome_options.add_argument("--disable-backgrounding-occluded-windows")
        chrome_options.add_argument("--disable-renderer-backgrounding")
        chrome_options.add_argument("--disable-features=TranslateUI")
        chrome_options.add_argument("--disable-ipc-flooding-protection")
        chrome_options.add_argument("--disable-default-apps")
        chrome_options.add_argument("--disable-sync")
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--aggressive-cache-discard")

        # HTTP proxy specific options
        chrome_options.add_argument("--ignore-ssl-errors")
        chrome_options.add_argument("--ignore-certificate-errors")
        chrome_options.add_argument("--allow-running-insecure-content")
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--ignore-certificate-errors-spki-list")
        chrome_options.add_argument("--ignore-ssl-errors-list")

        # Add experimental options
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option("useAutomationExtension", False)

        service = Service(r"C:\Users\<USER>\Downloads\chromedriver-win64\chromedriver-win64\chromedriver.exe")
        service.creation_flags = 0x08000000

        try:
            if not self.use_proxy:
                # No proxy mode (kept for backward compatibility)
                driver = webdriver.Chrome(service=service, options=chrome_options)
                self._logger.info("Using regular webdriver (no proxy)")
            elif self.advanced_proxy:
                # Advanced proxy mode using selenium-wire (slower but more features)
                from seleniumwire import webdriver as seleniumwire_webdriver

                try:
                    proxy = self._get_working_proxy_for_seleniumwire()
                    seleniumwire_options = {
                        'proxy': proxy,
                        'verify_ssl': False,
                        'suppress_connection_errors': True,
                        'connection_timeout': 15,
                        'read_timeout': 15,
                    }
                    driver = seleniumwire_webdriver.Chrome(
                        service=service,
                        options=chrome_options,
                        seleniumwire_options=seleniumwire_options
                    )
                    self._logger.info(f"Using advanced proxy (selenium-wire): {proxy}")
                    # Add request interceptor for selenium-wire
                    driver.request_interceptor = self._add_headers_to_request
                except NoWorkingProxyError as e:
                    self._logger.warning(f"No working proxy found for selenium-wire: {str(e)}")
                    self._logger.warning(f"Attempted {e.attempted_proxies} proxies for selenium-wire")
                    self._logger.info("Attempting fallback to Chrome proxy...")
                    # Fall back to Chrome proxy
                    try:
                        proxy_address = self._get_working_proxy_address()
                        chrome_options.add_argument(f"--proxy-server=http://{proxy_address}")
                        driver = webdriver.Chrome(service=service, options=chrome_options)
                        self._logger.info(f"Using Chrome proxy fallback: {proxy_address}")
                    except NoWorkingProxyError as e2:
                        self._logger.error(f"All proxy methods failed: {str(e2)}")
                        self._logger.error(f"Total proxies attempted: selenium-wire({e.attempted_proxies}) + chrome({e2.attempted_proxies})")
                        self._logger.warning("Falling back to no proxy mode")
                        driver = webdriver.Chrome(service=service, options=chrome_options)
            else:
                # Fast proxy mode using Chrome's built-in proxy (faster than selenium-wire)
                try:
                    proxy_address = self._get_working_proxy_address()
                    chrome_options.add_argument(f"--proxy-server=http://{proxy_address}")
                    driver = webdriver.Chrome(service=service, options=chrome_options)
                    self._logger.info(f"Using fast Chrome proxy: {proxy_address}")
                except NoWorkingProxyError as e:
                    self._logger.warning(f"No working proxy found for fast mode: {str(e)}")
                    self._logger.warning(f"Attempted {e.attempted_proxies} proxies for fast mode")
                    self._logger.warning("Falling back to no proxy mode")
                    driver = webdriver.Chrome(service=service, options=chrome_options)

            # Set ultra-fast timeouts for maximum speed
            driver.set_page_load_timeout(10)  # Reduced from 20 to 10
            driver.set_script_timeout(8)      # Reduced from 15 to 8
            driver.implicitly_wait(2)         # Reduced from 3 to 2

            # Test driver functionality
            driver.execute_script("return navigator.userAgent;")

            return driver

        except Exception as e:
            self._logger.error(f"Failed to initialize Chrome driver: {str(e)}")
            raise DriverInitializationError("Failed to initialize Chrome driver") from e

    def _parse_price_for_product_fast(self, product: WebElement) -> str | None:
        """Fast price parsing - only try the most common selectors"""
        try:
            # Try only the most common price selector first
            price_elements = product.find_elements(By.XPATH, ".//span[contains(@class, 'a-price')]//span[contains(@class, 'a-offscreen')]")
            if price_elements:
                price_text = price_elements[0].text.strip()
                if price_text:
                    # Quick price formatting
                    cleaned = price_text.replace("€", "").replace("$", "").replace(" ", "").strip()
                    try:
                        if "," in cleaned:
                            price_float = float(cleaned.replace(".", "").replace(",", "."))
                        else:
                            price_float = float(cleaned)

                        if 0.01 <= price_float <= 100000:
                            return f"{price_float:.2f}".replace(".", ",")
                    except:
                        pass

            # Quick fallback - try price-whole
            price_elements = product.find_elements(By.XPATH, ".//span[contains(@class, 'a-price-whole')]")
            if price_elements:
                price_text = price_elements[0].text.strip()
                if price_text:
                    try:
                        price_float = float(price_text.replace(".", "").replace(",", "."))
                        if 0.01 <= price_float <= 100000:
                            return f"{price_float:.2f}".replace(".", ",")
                    except:
                        pass

        except:
            pass

        return None

    def _parse_product_data_fast(self, product: WebElement) -> Product:
        """Fast product parsing - minimal XPath attempts"""
        # Get ASIN first (fastest)
        asin_code = product.get_attribute("data-asin")
        if not asin_code:
            raise MissingProductDataError("No ASIN")

        # Try only the most common title selector
        title_elems = product.find_elements(By.XPATH, ".//h2//span")
        if not title_elems:
            title_elems = product.find_elements(By.XPATH, ".//a/h2/span")

        if not title_elems:
            raise MissingProductDataError("No title")

        title = title_elems[0].text.strip()
        if not title:
            raise MissingProductDataError("Empty title")

        # Try only the most common URL selector
        url_elems = product.find_elements(By.XPATH, ".//h2/a")
        if not url_elems:
            url_elems = product.find_elements(By.XPATH, ".//a[contains(@class, 'a-link-normal')]")

        if not url_elems:
            raise MissingProductDataError("No URL")

        url = url_elems[0].get_attribute("href")
        if not url:
            raise MissingProductDataError("Empty URL")

        # Fast price parsing
        price = self._parse_price_for_product_fast(product)
        if not price:
            raise MissingProductDataError("No price")

        return Product(title=title, url=url, asin_code=asin_code, price=price)

    def _parse_product_data(self, product: WebElement) -> Product:
        """Use fast parsing by default"""
        return self._parse_product_data_fast(product)

    def _get_products_from_page(self, url: str, driver) -> List[Product]:
        """Enhanced page scraping with better error detection and context"""
        page_title = None
        page_source_snippet = None
        current_url = None

        try:
            # Quick cleanup
            try:
                driver.delete_all_cookies()
                if hasattr(driver, 'requests'):
                    del driver.requests
            except:
                pass

            # Fast page loading
            self._logger.debug(f"Loading page: {url}")
            driver.get(url)

            # Get comprehensive page information for diagnostics
            try:
                page_title = driver.title
                current_url = driver.current_url
                self._logger.debug(f"Page loaded - Title: '{page_title}', URL: '{current_url}'")

                # Get page source snippet for debugging
                page_source = driver.page_source
                if page_source:
                    page_source_snippet = page_source[:1000]  # First 1000 chars for debugging

            except Exception as e:
                self._logger.warning(f"Error getting page information: {str(e)}")
                page_title = "Unknown"
                current_url = url

            # Ultra-quick page check
            try:
                WebDriverWait(driver, 4).until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            except TimeoutException:
                self._logger.warning("Page load timeout, continuing...")
                raise PageScrapingError("Page load timeout", url, page_title)

            # Enhanced blocking detection with more comprehensive checks
            blocking_indicators = [
                # CAPTCHA and verification
                ("//form[contains(@action, 'validateCaptcha')]", "CAPTCHA form detected"),
                ("//title[contains(text(), 'Robot Check')]", "Robot check page"),
                ("//h1[contains(text(), 'Enter the characters you see below')]", "Character verification required"),
                ("//input[@id='captchacharacters']", "CAPTCHA input field found"),

                # Error pages
                ("//div[contains(@class, 'a-alert-error')]", "Error alert on page"),
                ("//span[contains(text(), 'Sorry, we just need to make sure')]", "Verification required message"),
                ("//div[contains(text(), 'Something went wrong')]", "Generic error page"),
                ("//h1[contains(text(), 'Page Not Found')]", "404 error page"),
                ("//title[contains(text(), '503 Service Temporarily Unavailable')]", "503 service unavailable"),

                # Italian blocking messages (since you're scraping amazon.it)
                ("//title[contains(text(), 'Ci dispiace')]", "Italian 'We are sorry' page"),
                ("//h1[contains(text(), 'Ci dispiace')]", "Italian 'We are sorry' heading"),
                ("//span[contains(text(), 'riprova più tardi')]", "Italian 'try again later' message"),
                ("//div[contains(text(), 'Qualcosa è andato storto')]", "Italian 'Something went wrong' message"),

                # Access denied / blocking
                ("//title[contains(text(), 'Access Denied')]", "Access denied page"),
                ("//h1[contains(text(), 'Access Denied')]", "Access denied heading"),
                ("//div[contains(@class, 'error-page')]", "Generic error page class"),

                # Rate limiting
                ("//title[contains(text(), 'Too Many Requests')]", "Rate limiting detected"),
                ("//h1[contains(text(), 'Too Many Requests')]", "Rate limiting heading")
            ]

            blocking_detected = False
            blocking_reasons = []

            for xpath, description in blocking_indicators:
                elements = driver.find_elements(By.XPATH, xpath)
                if elements:
                    blocking_detected = True
                    blocking_reasons.append(description)
                    self._logger.warning(f"Blocking indicator found: {description}")

            if blocking_detected:
                detailed_reason = f"Multiple blocking indicators: {', '.join(blocking_reasons)}"
                self._logger.error(f"Amazon blocking detected on {current_url}")
                self._logger.error(f"Page title: '{page_title}'")
                self._logger.error(f"Blocking reasons: {detailed_reason}")
                if page_source_snippet:
                    self._logger.debug(f"Page source snippet: {page_source_snippet}")

                # Provide specific guidance based on blocking type
                if "Italian 'We are sorry' page" in blocking_reasons:
                    self._logger.error("SOLUTION SUGGESTIONS:")
                    self._logger.error("1. Try switching to a different proxy (will be attempted automatically)")
                    self._logger.error("2. Increase delays between requests")
                    self._logger.error("3. Try using advanced proxy mode: advanced_proxy=True")
                    self._logger.error("4. Consider using a VPN or different IP range")
                    self._logger.error("5. Try scraping during off-peak hours")

                raise AmazonBlockingError(detailed_reason, current_url)

            # Check for empty search results (different from no products)
            empty_result_indicators = [
                "//span[contains(text(), 'No results for')]",
                "//span[contains(text(), 'Nessun risultato per')]",  # Italian
                "//div[contains(@class, 's-no-outline')]//span[contains(text(), 'No results')]",
                "//h1[contains(text(), 'No results found')]"
            ]

            for xpath in empty_result_indicators:
                if driver.find_elements(By.XPATH, xpath):
                    self._logger.warning(f"Empty search results detected with: {xpath}")
                    raise PageScrapingError("No search results found for query", url, page_title)

            # Minimal scrolling for speed
            try:
                driver.execute_script("window.scrollTo(0, 1500);")
                time.sleep(0.2)  # Very quick
            except Exception as e:
                self._logger.debug(f"Error during page scrolling: {str(e)}")

            # Fast product finding with multiple selectors
            product_selectors = [
                ProductXPath.PRODUCTS,
                "//div[contains(@class, 's-result-item')]",
                "//div[@data-asin and @data-asin!='']",
                "//div[contains(@class, 'sg-col-inner')]//div[@data-component-type]"
            ]

            product_elements = []
            selector_used = None

            for selector in product_selectors:
                try:
                    product_elements = driver.find_elements(By.XPATH, selector)
                    if product_elements:
                        selector_used = selector
                        self._logger.debug(f"Found {len(product_elements)} elements with selector: {selector}")
                        break
                except Exception as e:
                    self._logger.debug(f"Error with selector '{selector}': {str(e)}")
                    continue

            if not product_elements:
                # Enhanced debugging information
                self._logger.error(f"No product elements found on page: {current_url}")
                self._logger.error(f"Page title: '{page_title}'")

                # Try to get more diagnostic information
                try:
                    body_text = driver.find_element(By.TAG_NAME, "body").text[:500]
                    self._logger.debug(f"Page body text (first 500 chars): {body_text}")
                except Exception as e:
                    self._logger.debug(f"Could not get body text: {str(e)}")

                # Log page source snippet for debugging
                if page_source_snippet:
                    self._logger.debug(f"Page source snippet: {page_source_snippet}")

                # Check if this might be a different type of Amazon page
                page_type_indicators = [
                    ("//div[@id='nav-main']", "Amazon navigation detected"),
                    ("//div[contains(@class, 'nav-logo')]", "Amazon logo detected"),
                    ("//form[@id='nav-search-bar-form']", "Amazon search bar detected"),
                    ("//div[@id='search']", "Search results container detected")
                ]

                page_indicators_found = []
                for xpath, description in page_type_indicators:
                    if driver.find_elements(By.XPATH, xpath):
                        page_indicators_found.append(description)

                if page_indicators_found:
                    self._logger.info(f"Amazon page elements detected: {', '.join(page_indicators_found)}")
                    error_msg = f"Valid Amazon page but no product elements found with any selector"
                else:
                    error_msg = f"Page does not appear to be a valid Amazon search results page"

                raise PageScrapingError(error_msg, current_url, page_title)

            # Fast parallel processing with more workers
            parsed_products = []
            with ThreadPoolExecutor(max_workers=4) as executor:
                future_to_product = {
                    executor.submit(self._parse_product_data, product): product
                    for product in product_elements
                }

                for future in concurrent.futures.as_completed(future_to_product):
                    try:
                        parsed_product = future.result()
                        parsed_products.append(parsed_product)
                    except MissingProductDataError:
                        continue
                    except Exception:
                        continue

            if parsed_products:
                self._logger.info(f"Successfully parsed {len(parsed_products)} products from {len(product_elements)} elements")
                return parsed_products
            else:
                raise PageScrapingError(f"No valid products could be parsed from {len(product_elements)} elements", url, page_title)

        except (AmazonBlockingError, PageScrapingError):
            # Re-raise these specific errors without modification
            raise
        except Exception as e:
            self._logger.warning(f"Unexpected error during page scraping: {str(e)}")
            raise PageScrapingError(f"Unexpected error: {str(e)}", url, page_title)

    def scrape_amazon_page(self, url: str, csv_path: str = None, max_pages: int = 20) -> List[Product]:
        """
        Retrieves a list of products from Amazon and compares with existing prices.

        Args:
            url (str): The starting Amazon page URL
            csv_path (str): Path to CSV file with existing prices
            max_pages (int): Maximum number of pages to scrape (default: 4)

        Returns:
            List[Product]: A list of Product objects with price comparison data
        """
        self._logger.info(f"Scraping Amazon product data (up to {max_pages} pages)...")

        driver = None
        try:
            driver = self._init_chrome_driver()
        except Exception as e:
            self._logger.error(f"Failed to initialize driver: {str(e)}")
            raise DriverInitializationError from e

        all_products = []
        current_url = url
        current_page = 1

        try:
            while current_url and current_page <= max_pages:
                self._logger.info(f"Scraping page {current_page}...")

                try:
                    products = self._get_products_from_page(current_url, driver)
                except AmazonBlockingError as e:
                    self._logger.error(f"Amazon blocking detected on page {current_page}: {str(e)}")
                    self._logger.error(f"Blocking URL: {current_url}")
                    self._logger.error(f"Error details: {e.reason}")

                    # Log current proxy information for debugging
                    if self.use_proxy:
                        if hasattr(self, '_cached_proxy_address'):
                            self._logger.info(f"Current proxy: {self._cached_proxy_address}")
                        else:
                            self._logger.info("No cached proxy address available")
                    else:
                        self._logger.info("Running without proxy")

                    # For blocking errors, try different strategies
                    if self.retry_count < self.max_retries:
                        self.retry_count += 1
                        self._logger.info(f"Attempting retry {self.retry_count}/{self.max_retries} with different strategy...")

                        # Strategy 1: Wait longer and try again
                        if self.retry_count == 1:
                            wait_time = random.uniform(10, 20)
                            self._logger.info(f"Strategy 1: Waiting {wait_time:.1f} seconds before retry...")
                            time.sleep(wait_time)
                            continue

                        # Strategy 2: Reinitialize driver with different proxy
                        elif self.retry_count == 2:
                            self._logger.info("Strategy 2: Reinitializing driver with different proxy...")
                            try:
                                driver.quit()
                            except Exception as quit_error:
                                self._logger.debug(f"Error quitting driver: {str(quit_error)}")

                            # Clear cached proxies to force new selection
                            if hasattr(self, '_cached_proxy_address'):
                                old_proxy = self._cached_proxy_address
                                delattr(self, '_cached_proxy_address')
                                self._logger.info(f"Cleared cached proxy: {old_proxy}")
                            if hasattr(self, '_cached_seleniumwire_proxy'):
                                delattr(self, '_cached_seleniumwire_proxy')
                                self._logger.info("Cleared cached selenium-wire proxy")

                            try:
                                driver = self._init_chrome_driver()
                                self._logger.info("Driver reinitialized successfully")
                                continue
                            except Exception as reinit_error:
                                self._logger.error(f"Driver reinitialization failed: {str(reinit_error)}")

                        # Strategy 3: Switch to no-proxy mode
                        elif self.retry_count == 3:
                            self._logger.warning("Strategy 3: Switching to no-proxy mode as last resort...")
                            try:
                                driver.quit()
                            except Exception as quit_error:
                                self._logger.debug(f"Error quitting driver: {str(quit_error)}")

                            original_proxy_setting = self.use_proxy
                            self.use_proxy = False
                            self._logger.info(f"Proxy setting changed from {original_proxy_setting} to {self.use_proxy}")

                            try:
                                driver = self._init_chrome_driver()
                                self._logger.info("No-proxy driver initialized successfully")
                                continue
                            except Exception as reinit_error:
                                self._logger.error(f"No-proxy driver initialization failed: {str(reinit_error)}")
                                # Restore original proxy setting
                                self.use_proxy = original_proxy_setting

                    # If all retries failed, skip this page
                    self._logger.error(f"All retry strategies failed for page {current_page}, skipping to next page...")
                    self._logger.error(f"Total products collected so far: {len(all_products)}")
                    current_page += 1
                    continue

                except PageScrapingError as e:
                    self._logger.error(f"Page scraping error on page {current_page}: {str(e)}")
                    self._logger.error(f"Error URL: {e.url if hasattr(e, 'url') else current_url}")
                    self._logger.error(f"Page title: {e.page_title if hasattr(e, 'page_title') else 'Unknown'}")
                    self._logger.error(f"Error reason: {e.reason if hasattr(e, 'reason') else str(e)}")

                    # For page scraping errors, try simpler recovery
                    if "timeout" in str(e).lower() and self.retry_count < 2:
                        self.retry_count += 1
                        self._logger.info(f"Timeout detected - retrying page {current_page} (attempt {self.retry_count}/2)...")
                        wait_time = random.uniform(3, 7)
                        self._logger.info(f"Waiting {wait_time:.1f} seconds before retry...")
                        time.sleep(wait_time)
                        continue
                    elif "no product elements found" in str(e).lower():
                        self._logger.warning(f"No products found on page {current_page} - this might be expected")
                        self._logger.info(f"Skipping page {current_page} and continuing to next page...")
                        current_page += 1
                        continue
                    else:
                        # Skip this page for other scraping errors
                        self._logger.warning(f"Skipping page {current_page} due to scraping error")
                        current_page += 1
                        continue

                except Exception as e:
                    self._logger.error(f"Unexpected error on page {current_page}: {str(e)}")
                    self._logger.error(f"Error type: {type(e).__name__}")
                    self._logger.error(f"Current URL: {current_url}")

                    # Try to reinitialize driver if it seems to be dead
                    driver_dead_indicators = [
                        "target window already closed",
                        "session",
                        "chrome not reachable",
                        "disconnected",
                        "no such window"
                    ]

                    if any(indicator in str(e).lower() for indicator in driver_dead_indicators):
                        self._logger.warning("Driver appears to be dead, attempting to reinitialize...")
                        try:
                            driver.quit()
                        except Exception as quit_error:
                            self._logger.debug(f"Error quitting dead driver: {str(quit_error)}")

                        try:
                            self._logger.info("Reinitializing driver after crash...")
                            driver = self._init_chrome_driver()
                            self._logger.info("Driver reinitialized successfully, retrying current page...")
                            products = self._get_products_from_page(current_url, driver)
                        except Exception as reinit_error:
                            self._logger.error(f"Driver reinitialization failed: {str(reinit_error)}")
                            self._logger.error("Unable to recover from driver crash, terminating scraping...")
                            break
                    else:
                        # For other errors, skip this page and continue
                        self._logger.warning(f"Skipping page {current_page} due to unexpected error")
                        current_page += 1
                        continue

                # Reset retry count on successful page scraping
                self.retry_count = 0

                # Handle price checking based on proxy mode
                if not self.use_proxy:  # No proxy mode - skip individual price checks for speed
                    pass  # Skip individual price checks for speed
                elif self.advanced_proxy:  # Advanced proxy mode - do individual price checks
                    # Compare prices and add price change information
                    for product in products:
                        try:
                            self._check_price_drop(product)
                        except Exception as e:
                            self._logger.warning(f"Error checking price drop for product {product.asin_code}: {str(e)}")
                else:  # Fast proxy mode - skip individual checks, do batch at end
                    pass  # Skip individual price checks for speed

                all_products.extend(products)
                self._logger.info(f"Found {len(products)} products on page {current_page}")

                # Clear request queue before loading next page (if using selenium-wire)
                try:
                    if hasattr(driver, 'requests'):
                        del driver.requests
                except Exception as e:
                    self._logger.debug(f"Error clearing requests: {str(e)}")

                # Check for next page
                try:
                    current_url = self._has_next_page(driver)
                    if not current_url:
                        self._logger.info("No more pages available")
                        break
                except Exception as e:
                    self._logger.warning(f"Error checking for next page: {str(e)}")
                    break

                current_page += 1

                # Add delays based on proxy mode
                if current_page <= max_pages:
                    if self.advanced_proxy:
                        # Longer delays for advanced proxy mode to avoid detection
                        delay = random.uniform(1.0, 2.5)
                        self._logger.debug(f"Advanced proxy delay: {delay:.1f} seconds...")
                        time.sleep(delay)
                    elif self.use_proxy:
                        # Minimal delays for fast proxy mode
                        delay = random.uniform(0.5, 1.5)
                        self._logger.debug(f"Fast proxy delay: {delay:.1f} seconds...")
                        time.sleep(delay)
                    # No delay for no-proxy mode

            # Batch processing for modes that skip individual price checks
            if (not self.use_proxy or (self.use_proxy and not self.advanced_proxy)) and all_products:
                mode_name = "no-proxy" if not self.use_proxy else "fast-proxy"
                self._logger.info(f"Performing {mode_name} batch price checking...")
                for product in all_products:
                    try:
                        self._check_price_drop(product)
                    except Exception as e:
                        self._logger.debug(f"Error checking price drop for product {product.asin_code}: {str(e)}")

            self._logger.info(f"Total products scraped: {len(all_products)}")
            return all_products

        except Exception as e:
            self._logger.error(f"Fatal error during scraping: {str(e)}")
            self._logger.error(f"Fatal error type: {type(e).__name__}")
            self._logger.error(f"Current page when error occurred: {current_page}")
            self._logger.error(f"Total products collected before error: {len(all_products)}")
            raise DriverGetProductsError from e
        finally:
            # Provide scraping summary
            self._log_scraping_summary(len(all_products), current_page - 1, max_pages)

            if driver:
                try:
                    driver.quit()
                    self._logger.debug("Driver closed successfully")
                except Exception as e:
                    self._logger.debug(f"Error closing driver: {str(e)}")

    def _log_scraping_summary(self, total_products: int, pages_processed: int, max_pages: int) -> None:
        """Log a comprehensive summary of the scraping session"""
        self._logger.info("=" * 60)
        self._logger.info("SCRAPING SESSION SUMMARY")
        self._logger.info("=" * 60)
        self._logger.info(f"Total products collected: {total_products}")
        self._logger.info(f"Pages processed: {pages_processed}/{max_pages}")
        self._logger.info(f"Proxy mode: {'Advanced' if self.advanced_proxy else 'Fast' if self.use_proxy else 'No proxy'}")

        if hasattr(self, '_cached_proxy_address') and self._cached_proxy_address:
            self._logger.info(f"Final proxy used: {self._cached_proxy_address}")

        # Log proxy health statistics
        if self.proxy_health.proxy_failures:
            failed_proxies = len([p for p, count in self.proxy_health.proxy_failures.items() if count > 0])
            self._logger.info(f"Proxy failures encountered: {failed_proxies} proxies had failures")

        if self.proxy_health.proxy_blacklist:
            self._logger.info(f"Blacklisted proxies: {len(self.proxy_health.proxy_blacklist)}")

        self._logger.info("=" * 60)

    def _has_next_page(self, driver) -> str | None:
        """Check if there's a next page and return its URL"""
        try:
            # Try multiple selectors for next page
            for xpath in [
                "//a[contains(@class,'s-pagination-next')]",
                "//a[contains(@class,'s-pagination-item') and contains(@href,'page=')]",
                "//span[@class='s-pagination-strip']/a[contains(@href,'page=')]"
            ]:
                elements = driver.find_elements(By.XPATH, xpath)
                for element in elements:
                    if element.is_displayed() and "disabled" not in element.get_attribute("class"):
                        href = element.get_attribute("href")
                        if href and "page=" in href:
                            self._logger.debug(f"Found next page URL: {href}")
                            return href

            self._logger.debug("No next page found")
            return None
        except Exception as e:
            self._logger.warning(f"Error checking for next page: {str(e)}")
            return None

    def _read_existing_prices(self, csv_path: str) -> dict:
        """
        Read existing prices from CSV file.

        Args:
            csv_path (str): Path to the CSV file

        Returns:
            dict: Dictionary with ASIN codes as keys and prices as values
        """
        if not os.path.exists(csv_path):
            return {}

        try:
            df = pd.read_csv(csv_path)
            prices = {}

            for _, row in df.iterrows():
                if pd.notna(row['price']) and pd.notna(row['asin_code']):
                    # Use the price exactly as it appears in the CSV
                    prices[row['asin_code']] = row['price']

            return prices

        except Exception as e:
            self._logger.error(f"Error reading CSV file: {e}")
            return {}

    def _send_discord_alert(self, product: Product) -> None:
        """
        Sends a Discord webhook notification for significant price drops.

        Args:
            product (Product): Product with price change information
        """
        WEBHOOK_URL = "https://discord.com/api/webhooks/1364925973933457490/LZ0pgxZkJcjPVI0taYi-jitwxsSj6oYI3i2yNgUlvD6BVuGOrri8J4IH9A3t-TCnX9nS"

        try:
            # Safely get product attributes with defaults
            title = getattr(product, 'title', 'Unknown Product')[:1024]  # Truncate to Discord limit
            old_price = getattr(product, 'old_price', 0.0)
            current_price = self.extract_price(product)
            price_change_pct = getattr(product, 'price_change_pct', 0.0)
            product_url = getattr(product, 'url', None)
            asin_code = getattr(product, 'asin_code', 'Unknown')

            # Create Discord embed with safe value handling
            embed = {
                "title": "🔥 Significant Price Drop Alert! 🔥",
                "color": 0x00ff00,
                "fields": [
                    {
                        "name": "Product",
                        "value": title if title else "Unknown Product",
                        "inline": False
                    },
                    {
                        "name": "Old Price",
                        "value": f"€{old_price:.2f}" if old_price else "N/A",
                        "inline": True
                    },
                    {
                        "name": "New Price",
                        "value": f"€{current_price:.2f}" if current_price else "N/A",
                        "inline": True
                    },
                    {
                        "name": "Price Drop",
                        "value": f"{price_change_pct:.1f}%" if price_change_pct else "N/A",
                        "inline": True
                    }
                ]
            }
            if product_url:
                embed["url"] = product_url
            payload = {
                "embeds": [embed],
                "username": "Amazon Price Alert",
                "avatar_url": "https://cdn-icons-png.flaticon.com/512/732/732241.png"
            }
            response = requests.post(WEBHOOK_URL, json=payload)
            response.raise_for_status()
            self._logger.info(f"Discord notification sent for product: {asin_code}")

        except AttributeError as e:
            self._logger.error(f"Missing product attribute: {str(e)}")
        except requests.RequestException as e:
            self._logger.error(f"Discord webhook request failed: {str(e)}")
        except Exception as e:
            self._logger.error(f"Unexpected error in Discord notification: {str(e)}")

    def extract_price(self, product: Product) -> float:
        try:
            if not product.price:
                return 0.0
            price_str = (str(product.price)
                        .replace('€', '')
                        .replace('$', '')
                        .replace(' ', '')
                        .strip())

            if ',' in price_str:
                price_str = price_str.replace('.', '').replace(',', '.')

            price_float = float(price_str)
            self._logger.debug(f"Extracted price {price_float} from {product.price}")
            return price_float

        except (ValueError, AttributeError) as e:
            self._logger.warning(f"Could not extract price from {product.price}: {str(e)}")
            return 0.0

    def _check_price_drop(self, product: Product) -> None:
        try:
            current_price = self.extract_price(product)
            #self._logger.debug(f"Checking price drop for {product.asin_code}")
            #self._logger.debug(f"Current price: {current_price}")

            if current_price <= 0:
                self._logger.warning(f"Invalid current price for product {product.title[:35]}")
                return
            price_history = self.db.get_price_history(product.asin_code)


            if price_history is not None:
                last_price, _, _ = price_history
                self._logger.debug(f"Using database last price: {last_price}")
            else:
                self._logger.info(f"New product detected: {product.asin_code}")
                self.db.save_product(product)
                return

            price_diff = current_price - last_price
            price_change_pct = ((current_price - last_price) / last_price) * 100
            product.old_price = last_price
            product.price_diff = price_diff
            product.price_change_pct = price_change_pct

            if price_change_pct <= -40.0:
                self._logger.info(
                    f"Significant price drop detected for {product.title[:35]}: "
                    f"{last_price:.2f} -> {current_price:.2f} ({price_change_pct:.1f}%)"
                )
                self._send_discord_alert(product)

            self.db.save_product(product)

        except Exception as e:
            self._logger.error(f"Error checking price drop for {product.title[:35]}: {str(e)}")
            raise

    def scrape_multiple_categories(self, category_urls: List[str], csv_path: str = None, max_pages: int = 5) -> List[Product]:
        self._logger.info(f"Scraping {len(category_urls)} categories concurrently...")

        all_products = []
        with ThreadPoolExecutor(max_workers=min(len(category_urls), 3)) as executor:
            future_to_url = {
                executor.submit(self.scrape_amazon_page, url, csv_path, max_pages): url
                for url in category_urls
            }
            for future in concurrent.futures.as_completed(future_to_url):
                url = future_to_url[future]
                try:
                    category_products = future.result()
                    self._logger.info(f"Category {url} returned {len(category_products)} products")
                    all_products.extend(category_products)
                except Exception as e:
                    self._logger.error(f"Category {url} failed with error: {str(e)}")

        self._logger.info(f"Total products scraped across all categories: {len(all_products)}")
        return all_products

    def _get_random_proxy(self) -> dict:
        """Returns a random proxy from the proxy pool"""
        proxy = random.choice(PROXY_LIST)
        return {
            "http": f"http://{proxy}",
            "https": f"http://{proxy}"
        }

    def _validate_proxy(self, proxy: dict, proxy_address: str = None) -> Tuple[bool, str]:
        """Validates if proxy is working with detailed error reporting"""
        if not proxy_address:
            proxy_address = proxy.get('http', 'unknown').replace('http://', '')

        # Check if proxy is blacklisted
        if self.proxy_health.is_blacklisted(proxy_address):
            reason = f"Proxy blacklisted (failed {self.proxy_health.get_failure_count(proxy_address)} times)"
            self._logger.debug(f"Skipping blacklisted proxy {proxy_address}: {reason}")
            return False, reason

        # Test both HTTP and HTTPS endpoints for HTTP-only proxies
        test_endpoints = [
            ('http://httpbin.org/ip', 'HTTP'),
            ('https://httpbin.org/ip', 'HTTPS')
        ]

        for endpoint, protocol in test_endpoints:
            try:
                self._logger.debug(f"Validating proxy {proxy_address} with {protocol} endpoint...")
                response = requests.get(
                    endpoint,
                    proxies=proxy,
                    timeout=8,  # Increased timeout for HTTP-only proxies
                    headers={'User-Agent': self._get_random_user_agent()},
                    verify=False  # Disable SSL verification for HTTP proxies
                )

                if response.status_code == 200:
                    self.proxy_health.record_success(proxy_address)
                    self._logger.debug(f"Proxy {proxy_address} validation successful with {protocol}")
                    return True, f"Success ({protocol})"
                else:
                    self._logger.debug(f"Proxy {proxy_address} {protocol} returned status {response.status_code}")

            except requests.exceptions.ConnectTimeout:
                self._logger.debug(f"Proxy {proxy_address} {protocol} connection timeout")
                continue
            except requests.exceptions.ProxyError:
                self._logger.debug(f"Proxy {proxy_address} {protocol} connection error")
                continue
            except requests.exceptions.SSLError:
                self._logger.debug(f"Proxy {proxy_address} {protocol} SSL error (expected for HTTP-only proxies)")
                if protocol == 'HTTP':
                    # If HTTP works but HTTPS fails with SSL error, that's still a working proxy
                    self.proxy_health.record_success(proxy_address)
                    return True, "Success (HTTP-only)"
                continue
            except requests.exceptions.RequestException as e:
                self._logger.debug(f"Proxy {proxy_address} {protocol} request error: {str(e)}")
                continue
            except Exception as e:
                self._logger.debug(f"Proxy {proxy_address} {protocol} unexpected error: {str(e)}")
                continue

        # If we get here, both HTTP and HTTPS failed
        reason = "Failed both HTTP and HTTPS validation"
        self.proxy_health.record_failure(proxy_address, reason)
        return False, reason

    def _get_working_proxy_address(self) -> str | None:
        """Get a working proxy address with enhanced error handling and health tracking"""
        if not hasattr(self, '_cached_proxy_address') or not self._cached_proxy_address:
            shuffled_proxies = PROXY_LIST.copy()
            random.shuffle(shuffled_proxies)

            # Filter out blacklisted proxies first
            available_proxies = [p for p in shuffled_proxies if not self.proxy_health.is_blacklisted(p)]

            if not available_proxies:
                self._logger.warning("All proxies are blacklisted, trying all proxies anyway...")
                available_proxies = shuffled_proxies

            tested_count = 0
            failed_reasons = []

            # Test more proxies if needed
            max_test_count = min(len(available_proxies), 5)  # Test up to 5 proxies

            for proxy_address in available_proxies[:max_test_count]:
                tested_count += 1
                proxy = {
                    "http": f"http://{proxy_address}",
                    "https": f"http://{proxy_address}"
                }

                is_valid, reason = self._validate_proxy(proxy, proxy_address)
                if is_valid:
                    self._logger.info(f"Found working proxy address: {proxy_address}")
                    self._cached_proxy_address = proxy_address
                    return proxy_address
                else:
                    failed_reasons.append(f"{proxy_address}: {reason}")

            # Log detailed failure information
            self._logger.warning(f"No working proxy addresses found after testing {tested_count} proxies")
            for failure in failed_reasons:
                self._logger.debug(f"Proxy failure: {failure}")

            # Raise specific error with context
            raise NoWorkingProxyError(tested_count)

        return self._cached_proxy_address

    def _get_working_proxy_for_seleniumwire(self) -> dict | None:
        """Get a working proxy for selenium-wire with enhanced error handling"""
        if not hasattr(self, '_cached_seleniumwire_proxy') or not self._cached_seleniumwire_proxy:
            shuffled_proxies = PROXY_LIST.copy()
            random.shuffle(shuffled_proxies)

            # Filter out blacklisted proxies first
            available_proxies = [p for p in shuffled_proxies if not self.proxy_health.is_blacklisted(p)]

            if not available_proxies:
                self._logger.warning("All proxies are blacklisted for selenium-wire, trying all proxies anyway...")
                available_proxies = shuffled_proxies

            tested_count = 0
            failed_reasons = []

            # Test more proxies for selenium-wire
            max_test_count = min(len(available_proxies), 5)

            for proxy_address in available_proxies[:max_test_count]:
                tested_count += 1
                proxy = {
                    "http": f"http://{proxy_address}",
                    "https": f"http://{proxy_address}"
                }

                is_valid, reason = self._validate_proxy(proxy, proxy_address)
                if is_valid:
                    self._logger.info(f"Found working selenium-wire proxy: {proxy_address}")
                    self._cached_seleniumwire_proxy = proxy
                    return proxy
                else:
                    failed_reasons.append(f"{proxy_address}: {reason}")

            # Log detailed failure information
            self._logger.warning(f"No working proxies found for selenium-wire after testing {tested_count} proxies")
            for failure in failed_reasons:
                self._logger.debug(f"Selenium-wire proxy failure: {failure}")

            self._cached_seleniumwire_proxy = None
            raise NoWorkingProxyError(tested_count)

        return self._cached_seleniumwire_proxy

    def _get_working_proxy(self) -> dict | None:
        """Legacy method - returns selenium-wire proxy for backward compatibility"""
        return self._get_working_proxy_for_seleniumwire()

    def _validate_proxy_fast(self, proxy: dict) -> bool:
        """Fast proxy validation - wrapper for backward compatibility"""
        is_valid, _ = self._validate_proxy(proxy)
        return is_valid

    def _handle_timeout_recovery(self, driver) -> bool:
        """Attempts to recover from a page timeout. Returns True if recovery successful."""
        try:
            self._logger.info("Attempting timeout recovery...")
            try:
                driver.execute_script("window.stop();")
            except:
                pass
            try:
                driver.delete_all_cookies()
                if hasattr(driver, 'requests'):
                    del driver.requests
            except:
                pass
            try:
                driver.execute_script("return document.readyState;")
                self._logger.info("Driver recovery successful")
                return True
            except:
                self._logger.warning("Driver appears to be unresponsive after recovery attempt")
                return False

        except Exception as e:
            self._logger.error(f"Recovery attempt failed: {str(e)}")
            return False